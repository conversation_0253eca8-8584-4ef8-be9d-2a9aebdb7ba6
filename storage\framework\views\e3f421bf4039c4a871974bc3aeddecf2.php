<?php $__env->startSection('title', 'Accueil - Location et Vente de Véhicules'); ?>
<?php $__env->startSection('meta_description', 'Découvrez notre large sélection de véhicules en location et vente. Service premium, prix compétitifs et véhicules vérifiés.'); ?>

<?php $__env->startSection('styles'); ?>
<!-- Styles principaux optimisés -->
<link rel="preload" href="<?php echo e(asset('css/banner_new.css')); ?>" as="style" onload="this.onload=null;this.rel='stylesheet'">
<link rel="preload" href="<?php echo e(asset('css/advantages.css')); ?>" as="style" onload="this.onload=null;this.rel='stylesheet'">
<link rel="preload" href="<?php echo e(asset('css/brands.css')); ?>" as="style" onload="this.onload=null;this.rel='stylesheet'">
<link rel="preload" href="<?php echo e(asset('css/badges.css')); ?>" as="style" onload="this.onload=null;this.rel='stylesheet'">
<link rel="preload" href="<?php echo e(asset('css/index.css')); ?>" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript>
    <link rel="stylesheet" href="<?php echo e(asset('css/banner_new.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/advantages.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/brands.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/badges.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/index.css')); ?>">
</noscript>
<!-- Font Awesome pour les icônes -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
<!-- Animation on Scroll library -->
<link rel="preload" href="https://unpkg.com/aos@next/dist/aos.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css"></noscript>
<!-- Swiper CSS pour le carousel -->
<link rel="preload" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css"></noscript>
<style>
    /* Styles optimisés pour les performances */
    :root {
        --primary-color: #007bff;
        --primary-hover: #0056b3;
        --secondary-color: #6c757d;
        --success-color: #28a745;
        --danger-color: #dc3545;
        --warning-color: #ff9800;
        --light-bg: #f8f9fa;
        --border-color: #dee2e6;
        --text-color: #333;
        --text-muted: #555;
        --shadow-light: 0 8px 20px rgba(0,0,0,0.1);
        --shadow-hover: 0 15px 30px rgba(0,0,0,0.15);
        --transition-fast: all 0.2s ease;
        --transition-normal: all 0.3s ease;
        --border-radius: 6px;
        --border-radius-lg: 12px;
    }

    /* Style pour le loader dans les selects */
    .select-wrapper {
        position: relative;
        width: 100%;
    }

    .loader-container {
        position: absolute;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
        display: none;
        z-index: 10;
    }

    .select-loader {
        width: 15px;
        height: 15px;
        border: 2px solid rgba(0, 0, 0, 0.1);
        border-top: 2px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loader-visible {
        display: block;
    }

    /* Style pour désactiver les selects pendant le chargement */
    .form-select.disabled {
        opacity: 0.6;
        pointer-events: none;
        cursor: not-allowed;
    }
    
    /* Style pour le bouton de réinitialisation */
    .search-actions {
        display: flex;
        gap: 10px;
        margin-top: 10px;
        align-items: stretch;
    }

    .btn-reset-search {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 10px 15px;
        background-color: var(--secondary-color);
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-weight: 500;
        font-size: 0.9em;
        text-decoration: none;
        transition: var(--transition-normal);
        height: 44px;
        cursor: pointer;
    }

    .btn-reset-search:hover {
        background-color: #5a6268;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .btn-reset-search:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }

    .btn-reset-search i {
        margin-right: 6px;
    }

    /* Assurer que les boutons ont la même hauteur */
    .search-actions .btn-search,
    .search-actions .btn-reset-search {
        height: 44px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0 15px;
        font-size: 16px;
        box-sizing: border-box;
        min-width: 120px;
    }
    
    /* Style pour les filtres rapides */
    .quick-filters {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 15px;
        align-items: center;
    }

    .filter-label {
        font-weight: 600;
        color: var(--text-color);
        margin-right: 10px;
        font-size: 0.95em;
    }

    .quick-filter-btn {
        display: inline-flex;
        align-items: center;
        padding: 8px 15px;
        background-color: var(--light-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        color: #495057;
        font-weight: 500;
        font-size: 0.9em;
        transition: var(--transition-fast);
        cursor: pointer;
        text-decoration: none;
    }

    .quick-filter-btn:hover {
        background-color: #e9ecef;
        border-color: #ced4da;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .quick-filter-btn:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }

    .quick-filter-btn i {
        margin-right: 8px;
        font-size: 1.1em;
    }

    .quick-filter-btn[data-type="location"] {
        background-color: #e8f4ff;
        border-color: #b8daff;
        color: var(--primary-hover);
    }

    .quick-filter-btn[data-type="location"]:hover {
        background-color: #d1ecf1;
        border-color: #a6d5fa;
    }

    .quick-filter-btn[data-type="vente"] {
        background-color: #e8fff0;
        border-color: #b8ffd8;
        color: var(--success-color);
    }

    .quick-filter-btn[data-type="vente"]:hover {
        background-color: #d4edda;
        border-color: #a3d9a4;
    }
    
    /* Style pour les dividers de section */
    .section-divider {
        height: 3px;
        width: 60px;
        background: linear-gradient(90deg, #007bff, #00c6ff);
        margin: 15px auto 30px;
        border-radius: 2px;
    }
    
    .section-divider.light {
        background: linear-gradient(90deg, #ffffff, rgba(255,255,255,0.7));
    }
    
    /* Améliorations pour les cartes de véhicules */
    .vehicles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 30px;
        margin-top: 30px;
    }

    @media (max-width: 768px) {
        .vehicles-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
    }

    .vehicle-card {
        transition: var(--transition-normal);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-light);
        background-color: #fff;
        position: relative;
        border: 1px solid rgba(0,0,0,0.05);
        margin-bottom: 0;
        will-change: transform;
    }
    
    /* Styles pour Swiper */
    .swiper {
        width: 100%;
        padding-bottom: 50px;
    }
    
    .swiper-slide {
        height: auto;
    }
    
    .swiper-pagination {
        bottom: 0;
    }
    
    .swiper-button-next, 
    .swiper-button-prev {
        color: #007bff;
    }
    
    .featured-card {
        border: 2px solid #007bff;
    }
    
    .featured-overlay {
        position: absolute;
        top: 15px;
        left: 15px;
        z-index: 2;
    }
    
    .featured-tag {
        background-color: #ff9800;
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
    }
    
    .featured-tag i {
        margin-right: 5px;
    }
    
    .vehicle-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-hover);
        border-color: rgba(0, 123, 255, 0.2);
    }

    .vehicle-image {
        position: relative;
        height: 220px;
        overflow: hidden;
        background-color: var(--light-bg);
    }

    .vehicle-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .vehicle-card:hover .vehicle-image img {
        transform: scale(1.05);
    }

    .vehicle-info {
        padding: 20px;
    }

    .vehicle-title {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 12px;
        color: var(--text-color);
        line-height: 1.3;
    }
    
    .vehicle-details, .vehicle-specs {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-bottom: 15px;
    }
    
    .detail, .spec {
        font-size: 0.9rem;
        color: var(--text-muted);
        display: flex;
        align-items: center;
        background-color: var(--light-bg);
        padding: 5px 10px;
        border-radius: 20px;
        white-space: nowrap;
    }

    .detail i, .spec i {
        margin-right: 5px;
        color: var(--primary-color);
        flex-shrink: 0;
    }

    .vehicle-pricing {
        margin-top: 15px;
        border-top: 1px solid #eee;
        padding-top: 15px;
    }

    .price-location, .price-sale {
        margin-bottom: 15px;
    }

    .location {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        padding: 8px 12px;
        background-color: var(--light-bg);
        border-radius: var(--border-radius);
        border: 1px solid rgba(0,0,0,0.05);
    }

    .city {
        font-weight: 500;
        display: flex;
        align-items: center;
        color: var(--text-muted);
    }

    .city i {
        margin-right: 5px;
        color: var(--primary-color);
    }

    .price {
        font-weight: 700;
        color: var(--primary-color);
        font-size: 1.1em;
    }

    .price i {
        margin-right: 5px;
    }
    
    .btn-louer, .btn-acheter {
        display: block;
        width: 100%;
        padding: 12px;
        text-align: center;
        background-color: #007bff;
        color: white;
        border-radius: 6px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        border: none;
    }
    
    .btn-louer:hover, .btn-acheter:hover {
        background-color: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 86, 179, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .btn-louer.disabled {
        background-color: #6c757d;
        cursor: not-allowed;
    }
    
    .location-badge, .vente-badge, .mission-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
        z-index: 2;
    }
    
    .location-badge {
        background-color: #007bff;
        color: white;
    }
    
    .vente-badge {
        background-color: #28a745;
        color: white;
    }
    
    .mission-badge {
        background-color: #dc3545;
        color: white;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Banner Section -->
<section class="banner banner-bg">
    <div class="banner-overlay"></div>
    <div class="motion-box">
        <div class="motion-line"></div>
        <div class="motion-line"></div>
        <div class="motion-line"></div>
    </div>
    <div class="floating-elements">
        <div class="floating-icon" style="top: 15%; left: 10%;" data-aos="fade-down" data-aos-delay="100">
            <i class="fas fa-car"></i>
        </div>
        <div class="floating-icon" style="top: 25%; right: 15%;" data-aos="fade-down" data-aos-delay="200">
            <i class="fas fa-key"></i>
        </div>
        <div class="floating-icon" style="bottom: 30%; left: 18%;" data-aos="fade-up" data-aos-delay="300">
            <i class="fas fa-map-marker-alt"></i>
        </div>
        <div class="floating-icon" style="bottom: 20%; right: 10%;" data-aos="fade-up" data-aos-delay="400">
            <i class="fas fa-tag"></i>
        </div>
    </div>
    <div class="container">
        <div class="trust-badges" data-aos="fade-down" data-aos-delay="100">
            <div class="trust-badge">
                <i class="fas fa-shield-alt"></i>
                <span>100% Sécurisé</span>
            </div>
            <div class="trust-badge">
                <i class="fas fa-certificate"></i>
                <span>Qualité Premium</span>
            </div>
            <div class="trust-badge">
                <i class="fas fa-check-circle"></i>
                <span>Véhicules Vérifiés</span>
            </div>
        </div>
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="banner-content" data-aos="fade-right" data-aos-delay="200">
                    <div class="banner-badge">
                        <i class="fas fa-award"></i> Service Premium
                    </div>
                    <h1 class="banner-title">Trouvez le <span class="banner-accent">véhicule</span> idéal pour vos déplacements</h1>
                    <p class="banner-subtitle">Des véhicules soigneusement sélectionnés et approuvés par nos experts</p>
                    <div class="banner-features">
                        <div class="feature-item" data-aos="fade-up" data-aos-delay="300">
                            <i class="fas fa-check-circle"></i>
                            <span>Large sélection de véhicules</span>
                        </div>
                        <div class="feature-item" data-aos="fade-up" data-aos-delay="400">
                            <i class="fas fa-check-circle"></i>
                            <span>Prix compétitifs garantis</span>
                        </div>
                        <div class="feature-item" data-aos="fade-up" data-aos-delay="500">
                            <i class="fas fa-check-circle"></i>
                            <span>Service client 24/7</span>
                        </div>
                    </div>
                    <a href="#vehicules-location" class="banner-cta">Voir nos véhicules <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="banner-search-container" data-aos="fade-left" data-aos-delay="200">
                    <div class="quick-filters">
                        <span class="filter-label">Recherche rapide:</span>
                        <button class="quick-filter-btn" data-type="location"><i class="fas fa-car"></i>Location</button>
                        <button class="quick-filter-btn" data-type="vente"><i class="fas fa-tags"></i>Vente</button>
                        <a href="<?php echo e(route('index')); ?>" class="btn-reset-search"><i class="fas fa-sync-alt"></i> Réinitialiser</a>
                    </div>
                    <h3 class="search-form-title"><i class="fas fa-search"></i> Trouvez votre véhicule idéal</h3>
                    <form action="<?php echo e(route('index')); ?>" method="GET" id="search-form">
                        <div class="search-grid">
                            <div class="form-group">
                                <div class="select-wrapper">
                                    <select name="marque" id="brand" class="form-select">
                                        <option value="" selected disabled>Marque</option>
                                        <?php $__currentLoopData = $marques; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $marque): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($marque); ?>" <?php echo e(request('marque') == $marque ? 'selected' : ''); ?>><?php echo e(ucfirst($marque)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <div class="loader-container" id="loader-brand">
                                        <div class="select-loader"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="select-wrapper">
                                    <select name="type" id="type" class="form-select">
                                        <option value="" selected disabled>Type</option>
                                        <?php $__currentLoopData = $types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($type); ?>" <?php echo e(request('type') == $type ? 'selected' : ''); ?>><?php echo e(ucfirst($type)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <div class="loader-container" id="loader-type">
                                        <div class="select-loader"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="select-wrapper">
                                    <select name="serie" id="serie" class="form-select">
                                        <option value="" selected disabled>Série</option>
                                        <?php $__currentLoopData = $series; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $serie): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($serie); ?>" <?php echo e(request('serie') == $serie ? 'selected' : ''); ?>><?php echo e(ucfirst($serie)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <div class="loader-container" id="loader-serie">
                                        <div class="select-loader"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group wide-field">
                                <div class="select-wrapper">
                                    <select name="annee" id="year" class="form-select">
                                        <option value="" selected disabled>Année</option>
                                        <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($annee); ?>" <?php echo e(request('annee') == $annee ? 'selected' : ''); ?>><?php echo e($annee); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <div class="loader-container" id="loader-year">
                                        <div class="select-loader"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group wide-field">
                                <div class="select-wrapper">
                                    <select name="type_annonce" id="transaction" class="form-select">
                                        <option value="" selected disabled>Transaction</option>
                                        <option value="vente" <?php echo e(request('type_annonce') == 'vente' ? 'selected' : ''); ?>>Vente</option>
                                        <option value="location" <?php echo e(request('type_annonce') == 'location' ? 'selected' : ''); ?>>Location</option>
                                    </select>
                                    <div class="loader-container" id="loader-transaction">
                                        <div class="select-loader"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="search-actions" style="align-items: stretch;">
                            <button type="submit" class="btn-search" style="height: 44px; padding: 0 20px; display: flex; align-items: center; justify-content: center;"><i class="fas fa-search"></i> Rechercher</button>
                            
                        </div>
                    </form>
                    <div class="search-info">
                        <i class="fas fa-info-circle"></i>
                        <span>Plus de 500 véhicules disponibles</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Banner Section -->

<!----------------------------------------------------------------------------------------------------------------->

<!-- Section des voitures en location -->
<section id="vehicules-location" class="vehicles-section">
    <div class="container">
        <div class="section-header" data-aos="fade-up">
            <h2 class="section-title">Nos Véhicules en Location</h2>
            <p class="section-subtitle">Découvrez notre sélection de véhicules disponibles pour la location</p>
            <div class="section-divider"></div>
        </div>
        <div class="vehicles-grid">
            <?php if($vehiculesLocation->count() > 0): ?>
                <?php $__currentLoopData = $vehiculesLocation; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="vehicle-card" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>" data-disponible="<?php echo e($vehicule->disponibilite ? 'true' : 'false'); ?>">
                        <div class="vehicle-image">
                            <img src="<?php echo e(asset('storage/' . $vehicule->image_principale)); ?>" alt="<?php echo e($vehicule->marque); ?> <?php echo e($vehicule->serie); ?>">
                            <span class="<?php echo e($vehicule->disponibilite ? 'location-badge' : 'mission-badge'); ?>"><?php echo e($vehicule->disponibilite ? 'Location' : 'En Mission'); ?></span>
                        </div>
                        <div class="vehicle-info">
                            <h3 class="vehicle-title"><?php echo e($vehicule->marque); ?> <?php echo e($vehicule->serie); ?></h3>
                            <div class="vehicle-details">
                                <span class="detail"><i class="fas fa-car"></i> <?php echo e($vehicule->type_vehicule); ?></span>
                                <span class="detail"><i class="fas fa-tag"></i> <?php echo e($vehicule->serie); ?></span>
                                <span class="detail"><i class="fas fa-calendar-alt"></i> <?php echo e($vehicule->annee); ?></span>
                            </div>
                            <div class="vehicle-specs">
                                <span class="spec"><i class="fas fa-gas-pump"></i> <?php echo e($vehicule->carburant); ?></span>
                                <span class="spec"><i class="fas fa-cog"></i> <?php echo e($vehicule->transmission); ?></span>
                                <span class="spec"><i class="fas fa-users"></i> <?php echo e($vehicule->nb_places); ?> places</span>
                            </div>
                            <div class="vehicle-pricing">
                                <div class="price-location">
                                    <div class="location">
                                        <span class="city"><i class="fas fa-map-marker-alt"></i> Abidjan</span>
                                        <span class="price"><i class="fas fa-money-bill-wave"></i> <?php echo e(number_format($vehicule->prix_location_abidjan, 0, ',', '.')); ?>fr /Jr</span>
                                    </div>
                                    <div class="location">
                                        <span class="city"><i class="fas fa-map-marker-alt"></i> Interieur</span>
                                        <span class="price"><i class="fas fa-money-bill-wave"></i> <?php echo e(number_format($vehicule->prix_location_interieur, 0, ',', '.')); ?>fr /Jr</span>
                                    </div>
                                </div>
                                <?php if(auth()->guard()->check()): ?>
                                        <a href="<?php echo e($vehicule->disponibilite ? route('location.create', $vehicule->id) : '#'); ?>" class="btn-louer <?php echo e(!$vehicule->disponibilite ? 'disabled' : ''); ?>">
                                            <i class="fas fa-key"></i> <?php echo e($vehicule->disponibilite ? 'Louer' : 'Indisponible'); ?>

                                        </a>
                                    <?php else: ?>
                                        <a href="<?php echo e(route('auth.login')); ?>" class="btn-louer">
                                            <i class="fas fa-sign-in-alt"></i> Se connecter
                                        </a>
                                 <?php endif; ?>

                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <p>Aucun véhicule en location ne correspond à vos critères</p>
                    <a href="<?php echo e(route('index')); ?>" class="btn-reset">Réinitialiser les filtres</a>
                </div>
            <?php endif; ?>
        </div>
        <div class="pagination-container">
            <?php if(method_exists($vehiculesLocation, 'hasPages') && $vehiculesLocation->hasPages()): ?>
                <div class="pagination">
                    <?php if($vehiculesLocation->onFirstPage()): ?>
                        <span class="pagination-link disabled">
                            <i class="fas fa-chevron-left"></i>
                        </span>
                    <?php else: ?>
                        <a href="<?php echo e($vehiculesLocation->previousPageUrl()); ?>#vehicules-location" class="pagination-link">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php $__currentLoopData = $vehiculesLocation->getUrlRange(1, $vehiculesLocation->lastPage()); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e($url); ?>#vehicules-location" 
                           class="pagination-link <?php echo e($page == $vehiculesLocation->currentPage() ? 'active' : ''); ?>">
                            <?php echo e($page); ?>

                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    
                    <?php if($vehiculesLocation->hasMorePages()): ?>
                        <a href="<?php echo e($vehiculesLocation->nextPageUrl()); ?>#vehicules-location" class="pagination-link">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php else: ?>
                        <span class="pagination-link disabled">
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<!-- Fin section des voitures en location -->

<!-- Section des voitures en vente -->
<section id="vehicules-vente" class="vehicles-section">
    <div class="container">
        <div class="section-header" data-aos="fade-up">
            <h2 class="section-title">Nos Véhicules en Vente</h2>
            <p class="section-subtitle">Découvrez notre sélection de véhicules disponibles à l'achat</p>
            <div class="section-divider"></div>
        </div>
        <div class="vehicles-grid">
            <?php if($vehiculesVente->count() > 0): ?>
                <?php $__currentLoopData = $vehiculesVente; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="vehicle-card" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                        <div class="vehicle-image">
                            <img src="<?php echo e(asset('storage/' . $vehicule->image_principale)); ?>" alt="<?php echo e($vehicule->marque); ?> <?php echo e($vehicule->serie); ?>">
                            <span class="vente-badge">Vente</span>
                        </div>
                        <div class="vehicle-info">
                            <h3 class="vehicle-title"><?php echo e($vehicule->marque); ?> <?php echo e($vehicule->serie); ?></h3>
                            <div class="vehicle-details">
                                <span class="detail"><i class="fas fa-car"></i> <?php echo e($vehicule->type_vehicule); ?></span>
                                <span class="detail"><i class="fas fa-tag"></i> <?php echo e($vehicule->serie); ?></span>
                                <span class="detail"><i class="fas fa-calendar-alt"></i> <?php echo e($vehicule->annee); ?></span>
                            </div> 
                            <div class="vehicle-specs">
                                <span class="spec"><i class="fas fa-gas-pump"></i> <?php echo e($vehicule->carburant); ?></span>
                                <span class="spec"><i class="fas fa-cog"></i> <?php echo e($vehicule->transmission); ?></span>
                                <span class="spec"><i class="fas fa-users"></i> <?php echo e($vehicule->nb_places); ?> places</span>
                            </div>
                            <div class="vehicle-pricing">
                                <div class="price-sale">
                                    <span class="price"><i class="fas fa-money-bill-wave"></i> <?php echo e(number_format($vehicule->prix_vente, 0, ',', '.')); ?>fr</span>
                                </div>
                                <?php if(auth()->guard()->guest()): ?>
                                    <a href="<?php echo e(route('auth.login')); ?>" class="btn-acheter"><i class="fas fa-sign-in-alt"></i> Se connecter</a>
                                <?php else: ?>
                                    <a href="<?php echo e(route('vente.show', $vehicule->id)); ?>" class="btn-acheter"><i class="fas fa-shopping-cart"></i> Acheter</a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <p>Aucun véhicule en vente ne correspond à vos critères</p>
                    <a href="<?php echo e(route('index')); ?>" class="btn-reset">Réinitialiser les filtres</a>
                </div>
            <?php endif; ?>
        </div>
        <div class="pagination-container">
            <?php if(method_exists($vehiculesVente, 'hasPages') && $vehiculesVente->hasPages()): ?>
                <div class="pagination">
                    <?php if($vehiculesVente->onFirstPage()): ?>
                        <span class="pagination-link disabled">
                            <i class="fas fa-chevron-left"></i>
                        </span>
                    <?php else: ?>
                        <a href="<?php echo e($vehiculesVente->previousPageUrl()); ?>#vehicules-vente" class="pagination-link">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php $__currentLoopData = $vehiculesVente->getUrlRange(1, $vehiculesVente->lastPage()); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e($url); ?>#vehicules-vente" 
                           class="pagination-link <?php echo e($page == $vehiculesVente->currentPage() ? 'active' : ''); ?>">
                            <?php echo e($page); ?>

                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    
                    <?php if($vehiculesVente->hasMorePages()): ?>
                        <a href="<?php echo e($vehiculesVente->nextPageUrl()); ?>#vehicules-vente" class="pagination-link">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php else: ?>
                        <span class="pagination-link disabled">
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<!-- Fin section des voitures en vente -->

<!-- Section des avantages -->
<section class="advantages-section">
    <div class="container">
        <div class="section-header text-center" data-aos="fade-up">
            <h2 class="section-title text-white">Nos Avantages</h2>
            <p class="section-subtitle">Découvrez pourquoi nous sommes votre meilleur choix</p>
            <div class="section-divider light"></div>
        </div>
        <div class="advantages-grid">
            <!-- Qualité et expérience -->
            <div class="advantage-card" data-aos="fade-right" data-aos-delay="100">
                <div class="advantage-icon-wrapper">
                    <div class="advantage-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="advantage-badge">10+ ans</div>
                </div>
                <div class="advantage-content">
                    <h3>Qualité et expérience</h3>
                    <p>Expert depuis plus de 10 ans pour aider nos clients à louer et à acheter une voiture.</p>
                    <ul class="advantage-features">
                        <li><i class="fas fa-check"></i> Service professionnel</li>
                        <li><i class="fas fa-check"></i> Expertise reconnue</li>
                        <li><i class="fas fa-check"></i> Satisfaction client</li>
                    </ul>
                </div>
            </div>

            <!-- Économies -->
            <div class="advantage-card featured" data-aos="fade-up" data-aos-delay="200">
                <div class="advantage-icon-wrapper">
                    <div class="advantage-icon">
                        <i class="fas fa-piggy-bank"></i>
                    </div>
                    <div class="advantage-badge">Économies</div>
                </div>
                <div class="advantage-content">
                    <h3>Louez Malin, Payez Moins !</h3>
                    <p>Économisez plus en profitant de nos offres exceptionnelles pour la location de véhicule.</p>
                    <ul class="advantage-features">
                        <li><i class="fas fa-check"></i> Meilleurs prix garantis</li>
                        <li><i class="fas fa-check"></i> Offres spéciales régulières</li>
                        <li><i class="fas fa-check"></i> Programme de fidélité</li>
                    </ul>
                </div>
            </div>

            <!-- Boss -->
            <div class="advantage-card" data-aos="fade-left" data-aos-delay="300">
                <div class="advantage-icon-wrapper">
                    <div class="advantage-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="advantage-badge">VIP</div>
                </div>
                <div class="advantage-content">
                    <h3>Devenez le Boss que vous pensez être</h3>
                    <p>Louez et devenez le boss dont vous rêvez ! Un chauffeur qualifié et expérimenté à votre service.</p>
                    <ul class="advantage-features">
                        <li><i class="fas fa-check"></i> Chauffeur personnel</li>
                        <li><i class="fas fa-check"></i> Service premium</li>
                        <li><i class="fas fa-check"></i> Confort maximal</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Fin section des avantages -->

<!-- Section des marques -->
<section class="brands-section">
    <div class="container">
        <div class="section-header text-center" data-aos="fade-up">
            <h2 class="section-title">Nos Marques</h2>
            <p class="section-subtitle">Découvrez notre sélection de marques prestigieuses</p>
            <div class="section-divider"></div>
        </div>
        <div class="brands-slider">
            <div class="brands-track">
                <!-- Marque 1 -->
                <div class="brand-card">
                    <div class="brand-logo">
                        <img src="<?php echo e(asset('images/toyota.png')); ?>" alt="Toyota">
                    </div>
                    <h3>Toyota</h3>
                    <p class="brand-slogan">En Route vers l'Aventure</p>
                </div>

                <!-- Marque 2 -->
                <div class="brand-card">
                    <div class="brand-logo">
                        <img src="<?php echo e(asset('images/toyota.png')); ?>" alt="Mercedes">
                    </div>
                    <h3>Mercedes</h3>
                    <p class="brand-slogan">Le Meilleur ou Rien</p>
                </div>

                <!-- Marque 3 -->
                <div class="brand-card">
                    <div class="brand-logo">
                        <img src="<?php echo e(asset('images/toyota.png')); ?>" alt="BMW">
                    </div>
                    <h3>BMW</h3>
                    <p class="brand-slogan">Le Plaisir de Conduire</p>
                </div>

                <!-- Marque 4 -->
                <div class="brand-card">
                    <div class="brand-logo">
                        <img src="<?php echo e(asset('images/audi.png')); ?>" alt="Audi">
                    </div>
                    <h3>Audi</h3>
                    <p class="brand-slogan">L'Avance par la Technologie</p>
                </div>

                <!-- Marque 5 -->
                <div class="brand-card">
                    <div class="brand-logo">
                        <img src="<?php echo e(asset('images/honda.png')); ?>" alt="Honda">
                    </div>
                    <h3>Honda</h3>
                    <p class="brand-slogan">La Puissance des Rêves</p>
                </div>

                <!-- Marque 6 -->
                <div class="brand-card">
                    <div class="brand-logo">
                        <img src="<?php echo e(asset('images/toyota.png')); ?>" alt="Peugeot">
                    </div>
                    <h3>Peugeot</h3>
                    <p class="brand-slogan">Motion & Émotion</p>
                </div>

                <!-- Marque 7 -->
                <div class="brand-card">
                    <div class="brand-logo">
                        <img src="<?php echo e(asset('images/renault.png')); ?>" alt="Renault">
                    </div>
                    <h3>Renault</h3>
                    <p class="brand-slogan">La Passion pour la Vie</p>
                </div>

                <!-- Répétition des marques pour l'effet de défilement infini -->
                <div class="brand-card">
                    <div class="brand-logo">
                        <img src="<?php echo e(asset('images/toyota.png')); ?>" alt="Toyota">
                    </div>
                    <h3>Toyota</h3>
                    <p class="brand-slogan">En Route vers l'Aventure</p>
                </div>

                <div class="brand-card">
                    <div class="brand-logo">
                        <img src="<?php echo e(asset('images/toyota.png')); ?>" alt="Mercedes">
                    </div>
                    <h3>Mercedes</h3>
                    <p class="brand-slogan">Le Meilleur ou Rien</p>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Fin section des marques -->

<?php $__env->startSection('scripts'); ?>
<!-- Script pour l'animation de la bannière -->
<script src="<?php echo e(asset('js/banner.js')); ?>"></script>

<!-- Animation on Scroll library -->
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>

<!-- Script pour les fonctionnalités du formulaire de recherche dynamique -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Référence aux éléments select
        const marqueSelect = document.getElementById('brand');
        const typeSelect = document.getElementById('type');
        const serieSelect = document.getElementById('serie');
        const anneeSelect = document.getElementById('year');
        const transactionSelect = document.getElementById('transaction');
        
        // Référence aux loaders
        const loaderBrand = document.getElementById('loader-brand');
        const loaderType = document.getElementById('loader-type');
        const loaderSerie = document.getElementById('loader-serie');
        const loaderYear = document.getElementById('loader-year');
        const loaderTransaction = document.getElementById('loader-transaction');
        
        // Tableau de tous les selects et loaders
        const allSelects = [marqueSelect, typeSelect, serieSelect, anneeSelect, transactionSelect];
        const allLoaders = [loaderBrand, loaderType, loaderSerie, loaderYear, loaderTransaction];
        
        // Fonction pour afficher tous les loaders
        function showAllLoaders() {
            allLoaders.forEach(loader => {
                loader.classList.add('loader-visible');
            });
        }
        
        // Fonction pour cacher tous les loaders
        function hideAllLoaders() {
            allLoaders.forEach(loader => {
                loader.classList.remove('loader-visible');
            });
        }
        
        // Fonction pour désactiver tous les selects
        function disableAllSelects() {
            allSelects.forEach(select => {
                select.classList.add('disabled');
            });
        }
        
        // Fonction pour activer tous les selects
        function enableAllSelects() {
            allSelects.forEach(select => {
                select.classList.remove('disabled');
            });
        }
        
        // Associer les event listeners aux selects
        marqueSelect.addEventListener('change', function() { 
            showAllLoaders();
            disableAllSelects();
            updateOptions('marque', this.value); 
        });
        
        // Associer les event listeners aux boutons de filtres rapides
        document.querySelectorAll('.quick-filter-btn').forEach(button => {
            button.addEventListener('click', function() {
                const type = this.getAttribute('data-type');
                transactionSelect.value = type;
                
                // Déclencher l'événement change pour mettre à jour les autres filtres
                const event = new Event('change');
                transactionSelect.dispatchEvent(event);
                
                // Scroll vers la section appropriée
                const targetSection = type === 'location' ? 'vehicules-location' : 'vehicules-vente';
                document.getElementById(targetSection).scrollIntoView({ behavior: 'smooth' });
            });
        });
        
        typeSelect.addEventListener('change', function() { 
            showAllLoaders();
            disableAllSelects();
            updateOptions('type', this.value); 
        });
        
        serieSelect.addEventListener('change', function() { 
            showAllLoaders();
            disableAllSelects();
            updateOptions('serie', this.value); 
        });
        
        anneeSelect.addEventListener('change', function() { 
            showAllLoaders();
            disableAllSelects();
            updateOptions('annee', this.value); 
        });
        
        transactionSelect.addEventListener('change', function() { 
            showAllLoaders();
            disableAllSelects();
            updateOptions('type_annonce', this.value); 
        });
        
        // Réinitialisation des champs sur clic du bouton reset
        document.querySelector('.btn-reset-search').addEventListener('click', function(e) {
            e.preventDefault();
            
            // Réinitialiser tous les selects
            [marqueSelect, typeSelect, serieSelect, anneeSelect, transactionSelect].forEach(select => {
                select.value = '';
                select.selectedIndex = 0;
            });
            
            // Rediriger vers la page d'accueil
            window.location.href = this.getAttribute('href');
        });
        
        // Modifier le formulaire de recherche pour ajouter le scroll automatique
        document.getElementById('search-form').addEventListener('submit', function(e) {
            // Si un type d'annonce est sélectionné, ajoutez-le comme fragment d'URL
            if (transactionSelect.value) {
                const targetSection = transactionSelect.value === 'location' ? 'vehicules-location' : 'vehicules-vente';
                this.action = "<?php echo e(route('index')); ?>#" + targetSection;
            } else {
                // Par défaut, faire défiler jusqu'à la section de location
                this.action = "<?php echo e(route('index')); ?>#vehicules-location";
            }
        });
        
        // Fonction pour faire défiler la page jusqu'à la section appropriée après le chargement
        function scrollToTargetSection() {
            // Récupérer le fragment d'URL (partie après #)
            const hash = window.location.hash;
            
            // Si un fragment existe, faire défiler jusqu'à cette section
            if (hash) {
                // Petite temporisation pour s'assurer que la page est complètement chargée
                setTimeout(() => {
                    const targetElement = document.querySelector(hash);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth' });
                    }
                }, 100);
            } else {
                // Par défaut, faire défiler jusqu'à la section de location
                const defaultSection = document.getElementById('vehicules-location');
                if (defaultSection) {
                    setTimeout(() => {
                        defaultSection.scrollIntoView({ behavior: 'smooth' });
                    }, 100);
                }
            }
        }
        
        // Exécuter le défilement après le chargement de la page
        scrollToTargetSection();
        
        // Fonction pour mettre à jour les options des autres champs
        function updateOptions(changedField, selectedValue) {
            // Afficher un indicateur de chargement
            console.log('Mise à jour des options après changement du champ:', changedField);
            
            // Préparer les données pour la requête
            const formData = new FormData();
            formData.append('field', changedField);
            formData.append('value', selectedValue);
            
            // Ajouter les valeurs des autres champs
            if (marqueSelect.value && marqueSelect.value !== "") formData.append('marque', marqueSelect.value);
            if (typeSelect.value && typeSelect.value !== "") formData.append('type', typeSelect.value);
            if (serieSelect.value && serieSelect.value !== "") formData.append('serie', serieSelect.value);
            if (anneeSelect.value && anneeSelect.value !== "") formData.append('annee', anneeSelect.value);
            if (transactionSelect.value && transactionSelect.value !== "") formData.append('type_annonce', transactionSelect.value);
            
            // Envoyer la requête AJAX
            fetch('<?php echo e(route('filter.options')); ?>', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur réseau: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Données reçues:', data);
                
                // Mettre à jour chaque champ, sauf celui qui a été changé
                if (changedField !== 'marque' && data.data.marque) {
                    updateSelectOptions(marqueSelect, data.data.marque);
                }
                if (changedField !== 'type' && data.data.type) {
                    updateSelectOptions(typeSelect, data.data.type);
                }
                if (changedField !== 'serie' && data.data.serie) {
                    updateSelectOptions(serieSelect, data.data.serie);
                }
                if (changedField !== 'annee' && data.data.annee) {
                    updateSelectOptions(anneeSelect, data.data.annee);
                }
                
                // Cacher tous les loaders et réactiver tous les selects
                hideAllLoaders();
                enableAllSelects();
            })
            .catch(error => {
                console.error('Erreur:', error);
                // Cacher tous les loaders et réactiver tous les selects en cas d'erreur
                hideAllLoaders();
                enableAllSelects();
            });
        }
        
        // Fonction pour mettre à jour les options d'un select
        function updateSelectOptions(selectElement, options) {
            if (!options || !Array.isArray(options) || options.length === 0) {
                console.warn('Aucune option à mettre à jour pour:', selectElement.id);
                return;
            }
            
            console.log('Mise à jour des options pour:', selectElement.id, options);
            
            // Sauvegarder la valeur actuelle
            const currentValue = selectElement.value;
            
            // Conserver la première option (placeholder)
            const placeholder = selectElement.options[0].cloneNode(true);
            
            // Vider le select
            selectElement.innerHTML = '';
            
            // Remettre le placeholder
            selectElement.appendChild(placeholder);
            
            // Ajouter les nouvelles options
            options.forEach(option => {
                if (option) {  // Vérifier que l'option n'est pas null ou undefined
                    const optionElement = document.createElement('option');
                    optionElement.value = option;
                    optionElement.textContent = typeof option === 'string' ? 
                        (option.charAt(0).toUpperCase() + option.slice(1)) : option;
                    selectElement.appendChild(optionElement);
                }
            });
            
            // Restaurer la valeur si elle existe toujours dans les options
            if (options.includes(currentValue)) {
                selectElement.value = currentValue;
            }
        }
        
        // Ajuster la hauteur du bouton de réinitialisation pour qu'elle corresponde au bouton de recherche
        function adjustResetButtonHeight() {
            const searchButton = document.querySelector('.btn-search');
            const resetButton = document.querySelector('.btn-reset-search');
            
            if (searchButton && resetButton) {
                const searchButtonHeight = searchButton.offsetHeight;
                resetButton.style.height = searchButtonHeight + 'px';
                
                // Ajuster également les styles internes pour une meilleure apparence
                resetButton.style.display = 'inline-flex';
                resetButton.style.alignItems = 'center';
                resetButton.style.justifyContent = 'center';
                resetButton.style.padding = '0 15px';
                resetButton.style.boxSizing = 'border-box';
            }
        }
        
        // Exécuter l'ajustement une fois que la page est chargée
        adjustResetButtonHeight();
        
        // Réexécuter en cas de redimensionnement de la fenêtre
        window.addEventListener('resize', adjustResetButtonHeight);
    });
</script>

<!-- Swiper JS -->
<script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>

<!-- Script pour le slider des marques -->
<script src="<?php echo e(asset('js/index.js')); ?>"></script>

<!-- Initialisation de AOS (Animation on Scroll) et Swiper -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialisation de AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
        
        // Initialisation de Swiper si disponible
        if (typeof Swiper !== 'undefined') {
            try {
                // Swiper pour les marques
                const brandsSwiper = new Swiper('.brands-swiper', {
                    slidesPerView: 3,
                    spaceBetween: 20,
                    loop: true,
                    autoplay: {
                        delay: 3000,
                        disableOnInteraction: false
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev'
                    },
                    breakpoints: {
                        640: {
                            slidesPerView: 2,
                            spaceBetween: 15
                        },
                        768: {
                            slidesPerView: 3,
                            spaceBetween: 20
                        },
                        1024: {
                            slidesPerView: 4,
                            spaceBetween: 25
                        }
                    }
                });
                
                // Swiper pour les véhicules en vedette
                const featuredSwiper = new Swiper('.featured-swiper', {
                    slidesPerView: 1,
                    spaceBetween: 30,
                    loop: true,
                    autoplay: {
                        delay: 4000,
                        disableOnInteraction: false
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev'
                    },
                    breakpoints: {
                        640: {
                            slidesPerView: 1,
                            spaceBetween: 20
                        },
                        768: {
                            slidesPerView: 2,
                            spaceBetween: 30
                        },
                        1024: {
                            slidesPerView: 3,
                            spaceBetween: 40
                        }
                    }
                });
                
                console.log('Swiper initialized successfully');
            } catch (error) {
                console.error('Error initializing Swiper:', error);
            }
        } else {
            console.warn('Swiper is not defined');
        }
    });
</script>
<?php $__env->stopSection(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\memoire_app-master\memoire_app-master\resources\views/index.blade.php ENDPATH**/ ?>